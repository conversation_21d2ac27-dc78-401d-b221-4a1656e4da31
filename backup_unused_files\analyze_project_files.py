#!/usr/bin/env python3
"""
Project File Analysis Tool
Analyzes all Python files to find dependencies and identify unused files
"""

import os
import ast
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple

class ProjectAnalyzer:
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.python_files = []
        self.imports = {}
        self.file_dependencies = {}
        self.unused_files = set()
        
    def scan_python_files(self):
        """Scan for all Python files in the project"""
        self.python_files = list(self.project_root.glob("*.py"))
        print(f"📁 Found {len(self.python_files)} Python files")
        
    def analyze_imports(self):
        """Analyze imports in each Python file"""
        for file_path in self.python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Parse AST to find imports
                tree = ast.parse(content)
                imports = set()
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.add(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            imports.add(node.module)
                            # Also add specific imports
                            for alias in node.names:
                                imports.add(f"{node.module}.{alias.name}")
                
                # Also find string-based imports (like in try/except blocks)
                string_imports = re.findall(r'from\s+(\w+)\s+import|import\s+(\w+)', content)
                for match in string_imports:
                    for group in match:
                        if group:
                            imports.add(group)
                
                self.imports[file_path.name] = imports
                
            except Exception as e:
                print(f"⚠️  Error analyzing {file_path.name}: {e}")
                self.imports[file_path.name] = set()
    
    def find_dependencies(self):
        """Find which files depend on which other files"""
        for file_name, imports in self.imports.items():
            dependencies = set()
            
            for import_name in imports:
                # Check if this import corresponds to a local Python file
                for py_file in self.python_files:
                    module_name = py_file.stem  # filename without extension
                    
                    if (import_name == module_name or 
                        import_name.startswith(module_name + ".") or
                        import_name.endswith(module_name)):
                        dependencies.add(py_file.name)
            
            self.file_dependencies[file_name] = dependencies
    
    def find_unused_files(self):
        """Find files that are not imported by any other file"""
        all_files = {f.name for f in self.python_files}
        imported_files = set()
        
        for dependencies in self.file_dependencies.values():
            imported_files.update(dependencies)
        
        # Files that are never imported
        potentially_unused = all_files - imported_files
        
        # Filter out main entry points and test files
        main_files = {
            'app_ultra_fast.py',  # Main Streamlit app
            'run_ultra_fast.py',  # Launcher script
            'Dataset.py',         # Standalone dataset collection
            'Training.py',        # Standalone training
            'Detection.py',       # Standalone detection
        }
        
        test_files = {f for f in potentially_unused if f.startswith('test_')}
        standalone_files = {f for f in potentially_unused if f in main_files}
        
        # True unused files (not main files, not test files)
        self.unused_files = potentially_unused - main_files - test_files
        
        return {
            'unused': self.unused_files,
            'test_files': test_files,
            'standalone_main': standalone_files,
            'imported': imported_files
        }
    
    def identify_face_recognition_files(self):
        """Identify files specifically for face recognition"""
        face_recognition_files = {
            'detection_training': [],
            'modern_system': [],
            'test_files': []
        }
        
        # Check file contents for face recognition keywords
        face_keywords = ['face', 'recognition', 'LBPH', 'LBPHFaceRecognizer', 'haarcascade']
        
        for file_path in self.python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                
                if any(keyword.lower() in content for keyword in face_keywords):
                    if file_path.name in ['Detection.py', 'Training.py', 'Dataset.py']:
                        face_recognition_files['detection_training'].append(file_path.name)
                    elif file_path.name.startswith('test_'):
                        face_recognition_files['test_files'].append(file_path.name)
                    else:
                        face_recognition_files['modern_system'].append(file_path.name)
                        
            except Exception as e:
                print(f"⚠️  Error reading {file_path.name}: {e}")
        
        return face_recognition_files
    
    def generate_report(self):
        """Generate comprehensive analysis report"""
        print("\n" + "="*60)
        print("📊 PROJECT FILE ANALYSIS REPORT")
        print("="*60)
        
        # File dependencies
        print("\n🔗 FILE DEPENDENCIES:")
        for file_name, deps in self.file_dependencies.items():
            if deps:
                print(f"  {file_name} depends on: {', '.join(sorted(deps))}")
        
        # Unused files analysis
        unused_analysis = self.find_unused_files()
        
        print(f"\n🗑️  UNUSED FILES ANALYSIS:")
        print(f"  Total Python files: {len(self.python_files)}")
        print(f"  Files imported by others: {len(unused_analysis['imported'])}")
        print(f"  Standalone main files: {len(unused_analysis['standalone_main'])}")
        print(f"  Test files: {len(unused_analysis['test_files'])}")
        print(f"  Potentially unused: {len(unused_analysis['unused'])}")
        
        if unused_analysis['unused']:
            print(f"\n❌ TRULY UNUSED FILES (safe to delete):")
            for file in sorted(unused_analysis['unused']):
                print(f"    • {file}")
        
        if unused_analysis['test_files']:
            print(f"\n🧪 TEST FILES (keep for testing):")
            for file in sorted(unused_analysis['test_files']):
                print(f"    • {file}")
        
        if unused_analysis['standalone_main']:
            print(f"\n🚀 STANDALONE MAIN FILES (entry points):")
            for file in sorted(unused_analysis['standalone_main']):
                print(f"    • {file}")
        
        # Face recognition analysis
        face_files = self.identify_face_recognition_files()
        
        print(f"\n👤 FACE RECOGNITION FILES:")
        print(f"  Detection/Training (old system): {len(face_files['detection_training'])}")
        for file in sorted(face_files['detection_training']):
            print(f"    • {file}")
        
        print(f"  Modern system: {len(face_files['modern_system'])}")
        for file in sorted(face_files['modern_system']):
            print(f"    • {file}")
        
        print(f"  Test files: {len(face_files['test_files'])}")
        for file in sorted(face_files['test_files']):
            print(f"    • {file}")
        
        return unused_analysis, face_files

def main():
    """Main analysis function"""
    print("🔍 Starting Project File Analysis...")
    
    analyzer = ProjectAnalyzer()
    analyzer.scan_python_files()
    analyzer.analyze_imports()
    analyzer.find_dependencies()
    
    unused_analysis, face_files = analyzer.generate_report()
    
    print("\n" + "="*60)
    print("💡 RECOMMENDATIONS:")
    print("="*60)
    
    if unused_analysis['unused']:
        print("\n🗑️  FILES SAFE TO DELETE:")
        for file in sorted(unused_analysis['unused']):
            print(f"    rm {file}")
    
    print(f"\n👤 FACE RECOGNITION SYSTEM:")
    print(f"  OLD SYSTEM (2 files for detection/training):")
    for file in sorted(face_files['detection_training']):
        print(f"    • {file} - {'Training' if 'training' in file.lower() else 'Detection'}")
    
    print(f"  MODERN INTEGRATED SYSTEM:")
    for file in sorted(face_files['modern_system']):
        print(f"    • {file}")
    
    print(f"\n📋 SUMMARY:")
    print(f"  • You have {len(face_files['detection_training'])} old face recognition files")
    print(f"  • You have {len(face_files['modern_system'])} modern integrated files")
    print(f"  • You can safely delete {len(unused_analysis['unused'])} unused files")
    
    return unused_analysis, face_files

if __name__ == "__main__":
    main()
