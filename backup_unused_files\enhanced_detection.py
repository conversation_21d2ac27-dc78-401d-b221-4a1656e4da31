import cv2
import numpy as np
import os
import time

class EnhancedPersonDetector:
    def __init__(self, model_path='face_recognition_model.yml', 
                 cascade_path='C:/Users/<USER>/OneDrive/Desktop/RATNA/haarcascade_frontalface_default.xml'):
        """
        Enhanced person detector with improved accuracy
        """
        self.model_path = model_path
        self.cascade_path = cascade_path
        self.model = None
        self.face_classifier = None
        self.detection_history = []  # Store recent detections for smoothing
        self.confidence_threshold = 75
        
        self.load_model()
    
    def load_model(self):
        """Load the trained face recognition model and cascade classifier"""
        try:
            # Load face cascade
            if os.path.exists(self.cascade_path):
                self.face_classifier = cv2.CascadeClassifier(self.cascade_path)
                print("✅ Face cascade loaded successfully")
            else:
                print(f"❌ Face cascade not found: {self.cascade_path}")
                return False
            
            # Load trained model
            if os.path.exists(self.model_path):
                self.model = cv2.face.LBPHFaceRecognizer_create()
                self.model.read(self.model_path)
                print("✅ Face recognition model loaded successfully")
            else:
                print(f"❌ Model not found: {self.model_path}")
                print("Please run improved_training.py first to train the model")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False
    
    def extract_face(self, img):
        """Extract face from image with improved parameters"""
        if self.face_classifier is None:
            return None
            
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply histogram equalization for better detection
        gray = cv2.equalizeHist(gray)
        
        # Detect faces with optimized parameters
        faces = self.face_classifier.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=3,
            minSize=(30, 30),
            maxSize=(300, 300),
            flags=cv2.CASCADE_SCALE_IMAGE
        )
        
        if len(faces) == 0:
            return None
        
        # Return the largest face (most likely the main subject)
        largest_face = max(faces, key=lambda face: face[2] * face[3])
        x, y, w, h = largest_face
        
        return img[y:y+h, x:x+w], (x, y, w, h)
    
    def preprocess_face(self, face):
        """Preprocess face for recognition (same as training)"""
        # Resize to standard size
        face = cv2.resize(face, (200, 200))
        
        # Convert to grayscale if needed
        if len(face.shape) == 3:
            face = cv2.cvtColor(face, cv2.COLOR_BGR2GRAY)
        
        # Apply histogram equalization
        face = cv2.equalizeHist(face)
        
        return face
    
    def calculate_confidence(self, distance):
        """Calculate confidence score from LBPH distance"""
        if distance < 50:
            return 95
        elif distance < 70:
            return 85
        elif distance < 90:
            return 75
        elif distance < 110:
            return 65
        else:
            return max(0, 100 - int(distance * 0.8))
    
    def smooth_detection(self, current_result, current_confidence):
        """Smooth detection results over time to reduce flickering"""
        self.detection_history.append((current_result, current_confidence))
        
        # Keep only last 5 detections
        if len(self.detection_history) > 5:
            self.detection_history.pop(0)
        
        # If we have enough history, use majority voting
        if len(self.detection_history) >= 3:
            high_confidence_detections = [
                result for result, conf in self.detection_history 
                if conf > self.confidence_threshold
            ]
            
            if len(high_confidence_detections) >= 2:
                # Return most common high-confidence result
                return max(set(high_confidence_detections), 
                          key=high_confidence_detections.count)
        
        return current_result
    
    def detect_person(self, frame):
        """Detect and recognize person in frame"""
        if self.model is None:
            return frame, "Model not loaded", 0, (0, 0, 255)
        
        # Extract face
        face_result = self.extract_face(frame)
        if face_result is None:
            return frame, "No face detected", 0, (255, 0, 0)
        
        face, (x, y, w, h) = face_result
        
        # Preprocess face
        processed_face = self.preprocess_face(face)
        
        # Predict
        result = self.model.predict(processed_face)
        predicted_label = result[0]
        distance = result[1]
        
        # Calculate confidence
        confidence = self.calculate_confidence(distance)
        
        # Apply smoothing
        smoothed_result = self.smooth_detection(predicted_label, confidence)
        
        # Determine display text and color
        if confidence > self.confidence_threshold:
            display_text = f"Person {smoothed_result} Recognized"
            color = (0, 255, 0)  # Green
        elif confidence > 50:
            display_text = f"Person {predicted_label} (Uncertain)"
            color = (0, 255, 255)  # Yellow
        else:
            display_text = "Unknown Person"
            color = (0, 0, 255)  # Red
        
        # Draw face rectangle
        cv2.rectangle(frame, (x, y), (x+w, y+h), color, 2)
        
        # Add text with confidence and distance
        cv2.putText(frame, f"{display_text}", (x, y-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        cv2.putText(frame, f"Confidence: {confidence}%", (x, y+h+20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        cv2.putText(frame, f"Distance: {distance:.1f}", (x, y+h+40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return frame, display_text, confidence, color

def main():
    """Main detection loop"""
    print("🚀 Enhanced Person Detection System")
    print("=" * 50)
    
    # Initialize detector
    detector = EnhancedPersonDetector()
    
    if detector.model is None or detector.face_classifier is None:
        print("❌ Failed to initialize detector. Exiting.")
        return
    
    # Initialize camera
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("❌ Error: Could not open camera")
        return
    
    print("✅ Camera initialized successfully")
    print("📹 Starting live detection...")
    print("Press 'q' to quit, 's' to save current frame")
    
    frame_count = 0
    start_time = time.time()
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("❌ Error reading frame")
            break
        
        # Detect person
        processed_frame, status, confidence, color = detector.detect_person(frame)
        
        # Add FPS counter
        frame_count += 1
        elapsed_time = time.time() - start_time
        fps = frame_count / elapsed_time if elapsed_time > 0 else 0
        
        cv2.putText(processed_frame, f"FPS: {fps:.1f}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Display frame
        cv2.imshow('Enhanced Person Detection', processed_frame)
        
        # Handle key presses
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            # Save current frame
            filename = f"detection_frame_{int(time.time())}.jpg"
            cv2.imwrite(filename, processed_frame)
            print(f"📸 Frame saved as {filename}")
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    print("👋 Detection stopped")

if __name__ == "__main__":
    main()
