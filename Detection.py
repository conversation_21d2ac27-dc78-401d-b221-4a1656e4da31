import cv2
import numpy as np
from os import listdir
from os.path import isfile, join

# Use the same dataset path as in Dataset.py and Training.py
data_path = 'C:/Users/<USER>/OneDrive/Desktop/RATNA/dataset'
onlyfiles = [f for f in listdir(data_path) if isfile(join(data_path, f))]

Training_Data, Labels = [], []

for i, file in enumerate(onlyfiles):
    image_path = join(data_path, file)
    images = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
    if images is None:
        print(f"Warning: Unable to read {image_path}, skipping.")
        continue
    Training_Data.append(np.asarray(images, dtype=np.uint8))
    Labels.append(i)

if len(Training_Data) == 0:
    print("No valid images found for training.")
    exit()

Labels = np.asarray(Labels, dtype=np.int32)

model = cv2.face.LBPHFaceRecognizer_create()
model.train(np.asarray(Training_Data), np.asarray(Labels))

print("Dataset Model Training Completed")

face_classifier = cv2.CascadeClassifier('C:/Users/<USER>/OneDrive/Desktop/RATNA/haarcascade_frontalface_default.xml')

def face_extractor(img):
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # Improved face detection parameters for better accuracy
    faces = face_classifier.detectMultiScale(
        gray,
        scaleFactor=1.1,  # Smaller scale factor for better detection
        minNeighbors=3,   # Reduced for more sensitive detection
        minSize=(30, 30), # Minimum face size
        maxSize=(300, 300) # Maximum face size
    )
    if faces is None or len(faces) == 0:
        return None

    # Return the largest face detected (most likely the main subject)
    if len(faces) > 1:
        largest_face = max(faces, key=lambda face: face[2] * face[3])
        x, y, w, h = largest_face
    else:
        x, y, w, h = faces[0]

    return img[y:y+h, x:x+w]

cap = cv2.VideoCapture(0)

while True:
    ret, frame = cap.read()
    if not ret:
        break

    face = face_extractor(frame)
    if face is not None:
        face = cv2.resize(face, (200, 200))
        face_gray = cv2.cvtColor(face, cv2.COLOR_BGR2GRAY)

        # Apply same preprocessing as training
        face_gray = cv2.equalizeHist(face_gray)

        result = model.predict(face_gray)

        # Improved confidence calculation based on LBPH distance
        # Lower distance = higher confidence
        distance = result[1]
        if distance < 50:  # Very confident
            confidence = 95
        elif distance < 80:  # Good confidence
            confidence = 85
        elif distance < 120:  # Moderate confidence
            confidence = 70
        else:  # Low confidence
            confidence = max(0, 100 - int(distance))

        display_text = "Unknown Person"
        color = (0, 0, 255)
        if confidence > 75:  # Lowered threshold for better detection
            display_text = "Recognized Person"
            color = (0, 255, 0)  # Green for recognized
        elif confidence > 60:
            display_text = "Possible Match"
            color = (0, 255, 255)  # Yellow for possible match

        # Display confidence score along with recognition result
        cv2.putText(frame, f"{display_text} ({confidence}%)", (50, 50), cv2.FONT_HERSHEY_COMPLEX, 1, color, 2)
        cv2.putText(frame, f"Distance: {distance:.1f}", (50, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        cv2.rectangle(frame, (0, 0), (frame.shape[1], frame.shape[0]), color, 2)
    else:
        cv2.putText(frame, "Face Not Found", (50, 50), cv2.FONT_HERSHEY_COMPLEX, 1, (255, 0, 0), 2)

    cv2.imshow('Face Detector', frame)

    if cv2.waitKey(1) == 13:  # Enter key
        break

cap.release()
cv2.destroyAllWindows()