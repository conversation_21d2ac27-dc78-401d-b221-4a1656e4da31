import cv2
import numpy as np
import os
from pathlib import Path
import time

def download_haar_cascade():
    """Download Haar cascade if not present"""
    cascade_path = "haarcascade_frontalface_default.xml"
    
    if not os.path.exists(cascade_path):
        print("📥 Downloading Haar cascade classifier...")
        import urllib.request
        url = "https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_frontalface_default.xml"
        try:
            urllib.request.urlretrieve(url, cascade_path)
            print("✅ Haar cascade downloaded successfully")
        except Exception as e:
            print(f"❌ Failed to download Haar cascade: {e}")
            return False
    else:
        print("✅ Haar cascade found")
    
    return True

def test_dataset_quality():
    """Test the quality of training dataset"""
    print("\n🔍 Testing Dataset Quality...")
    print("=" * 50)
    
    dataset_path = "face_dataset/Shuvrajit Dey"
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset not found: {dataset_path}")
        return False
    
    image_files = [f for f in os.listdir(dataset_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    print(f"📊 Found {len(image_files)} images in dataset")
    
    if len(image_files) < 10:
        print("⚠️  Warning: Very few images for training. Recommend at least 50-100 images.")
    
    # Test a few sample images
    valid_images = 0
    total_variance = 0
    
    for i, img_file in enumerate(image_files[:10]):  # Test first 10 images
        img_path = os.path.join(dataset_path, img_file)
        img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        
        if img is not None:
            valid_images += 1
            variance = np.var(img)
            total_variance += variance
            print(f"  Image {i+1}: {img_file} - Shape: {img.shape}, Variance: {variance:.1f}")
        else:
            print(f"  ❌ Failed to load: {img_file}")
    
    avg_variance = total_variance / valid_images if valid_images > 0 else 0
    print(f"\n📈 Dataset Quality Summary:")
    print(f"  Valid images: {valid_images}/{len(image_files[:10])}")
    print(f"  Average variance: {avg_variance:.1f}")
    
    if avg_variance < 100:
        print("  ⚠️  Low variance detected - images might be too uniform")
    else:
        print("  ✅ Good image variance")
    
    return True

def train_with_current_dataset():
    """Train model with current dataset structure"""
    print("\n🚀 Training with Current Dataset...")
    print("=" * 50)
    
    dataset_path = "face_dataset/Shuvrajit Dey"
    
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset not found: {dataset_path}")
        return False
    
    # Load training data
    training_data = []
    labels = []
    
    image_files = [f for f in os.listdir(dataset_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    print(f"📚 Loading {len(image_files)} training images...")
    
    for i, img_file in enumerate(image_files):
        img_path = os.path.join(dataset_path, img_file)
        img = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
        
        if img is not None:
            # Standardize preprocessing
            img = cv2.resize(img, (200, 200))
            img = cv2.equalizeHist(img)
            
            training_data.append(img)
            labels.append(0)  # Single person, label = 0
            
            if (i + 1) % 20 == 0:
                print(f"  Processed {i + 1}/{len(image_files)} images...")
    
    if len(training_data) == 0:
        print("❌ No valid training data found")
        return False
    
    print(f"✅ Loaded {len(training_data)} training images")
    
    # Train model
    print("🧠 Training LBPH Face Recognizer...")
    model = cv2.face.LBPHFaceRecognizer_create()
    model.train(training_data, np.array(labels, dtype=np.int32))
    
    # Save model
    model_path = "trained_face_model.xml"
    model.save(model_path)
    print(f"💾 Model saved to {model_path}")
    
    return True

def test_live_detection():
    """Test live detection with trained model"""
    print("\n📹 Testing Live Detection...")
    print("=" * 50)
    
    # Check if model exists
    model_path = "trained_face_model.xml"
    if not os.path.exists(model_path):
        print(f"❌ Trained model not found: {model_path}")
        print("Please run training first")
        return False
    
    # Check cascade
    cascade_path = "haarcascade_frontalface_default.xml"
    if not os.path.exists(cascade_path):
        print(f"❌ Haar cascade not found: {cascade_path}")
        return False
    
    # Load model and cascade
    model = cv2.face.LBPHFaceRecognizer_create()
    model.read(model_path)
    face_cascade = cv2.CascadeClassifier(cascade_path)
    
    print("✅ Model and cascade loaded successfully")
    
    # Initialize camera
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("❌ Could not open camera")
        return False
    
    print("📷 Camera initialized")
    print("🎯 Starting detection test...")
    print("Press 'q' to quit, 's' to save frame, 'r' to reset stats")
    
    # Statistics
    frame_count = 0
    detection_count = 0
    recognition_count = 0
    start_time = time.time()
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Convert to grayscale for face detection
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        gray = cv2.equalizeHist(gray)
        
        # Detect faces
        faces = face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=3,
            minSize=(30, 30),
            maxSize=(300, 300)
        )
        
        for (x, y, w, h) in faces:
            detection_count += 1
            
            # Extract and preprocess face
            face_roi = gray[y:y+h, x:x+w]
            face_roi = cv2.resize(face_roi, (200, 200))
            face_roi = cv2.equalizeHist(face_roi)
            
            # Predict
            label, distance = model.predict(face_roi)
            
            # Calculate confidence
            if distance < 50:
                confidence = 95
                status = "High Confidence"
                color = (0, 255, 0)
                recognition_count += 1
            elif distance < 80:
                confidence = 85
                status = "Good Confidence"
                color = (0, 255, 0)
                recognition_count += 1
            elif distance < 120:
                confidence = 70
                status = "Moderate"
                color = (0, 255, 255)
            else:
                confidence = max(0, 100 - int(distance * 0.8))
                status = "Low Confidence"
                color = (0, 0, 255)
            
            # Draw results
            cv2.rectangle(frame, (x, y), (x+w, y+h), color, 2)
            
            if confidence > 70:
                text = f"Shuvrajit Dey ({confidence}%)"
            else:
                text = f"Unknown ({confidence}%)"
            
            cv2.putText(frame, text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            cv2.putText(frame, f"Distance: {distance:.1f}", (x, y+h+20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            cv2.putText(frame, status, (x, y+h+40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        # Display statistics
        elapsed_time = time.time() - start_time
        fps = frame_count / elapsed_time if elapsed_time > 0 else 0
        recognition_rate = (recognition_count / detection_count * 100) if detection_count > 0 else 0
        
        cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Detections: {detection_count}", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Recognitions: {recognition_count}", (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Recognition Rate: {recognition_rate:.1f}%", (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.imshow('Detection Test', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            filename = f"test_frame_{int(time.time())}.jpg"
            cv2.imwrite(filename, frame)
            print(f"📸 Frame saved as {filename}")
        elif key == ord('r'):
            detection_count = 0
            recognition_count = 0
            frame_count = 0
            start_time = time.time()
            print("📊 Statistics reset")
    
    cap.release()
    cv2.destroyAllWindows()
    
    print(f"\n📊 Final Test Results:")
    print(f"  Total detections: {detection_count}")
    print(f"  Successful recognitions: {recognition_count}")
    print(f"  Recognition rate: {recognition_rate:.1f}%")
    
    if recognition_rate > 80:
        print("✅ Excellent recognition performance!")
    elif recognition_rate > 60:
        print("✅ Good recognition performance")
    elif recognition_rate > 40:
        print("⚠️  Moderate recognition performance - consider more training data")
    else:
        print("❌ Poor recognition performance - needs improvement")
    
    return True

def main():
    """Main testing function"""
    print("🧪 Face Recognition Detection Test")
    print("=" * 50)
    
    # Step 1: Download cascade if needed
    if not download_haar_cascade():
        return
    
    # Step 2: Test dataset quality
    test_dataset_quality()
    
    # Step 3: Train model
    if train_with_current_dataset():
        print("\n✅ Training completed successfully!")
        
        # Step 4: Test live detection
        input("\nPress Enter to start live detection test...")
        test_live_detection()
    else:
        print("❌ Training failed")

if __name__ == "__main__":
    main()
